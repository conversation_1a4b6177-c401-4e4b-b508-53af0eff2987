{"train_batch_size": 4, "train_micro_batch_size_per_gpu": 1, "gradient_accumulation_steps": 4, "optimizer": {"type": "AdamW", "params": {"lr": 1e-06, "betas": [0.9, 0.999], "eps": 1e-08, "weight_decay": 0.01}}, "scheduler": {"type": "WarmupLR", "params": {"warmup_min_lr": 0, "warmup_max_lr": 1e-06, "warmup_num_steps": 100}}, "zero_optimization": {"stage": 3, "offload_optimizer": {"device": "cpu", "pin_memory": true}, "offload_param": {"device": "cpu", "pin_memory": true}, "overlap_comm": true, "contiguous_gradients": true, "sub_group_size": 1000000000.0, "reduce_bucket_size": "auto", "stage3_prefetch_bucket_size": "auto", "stage3_param_persistence_threshold": "auto", "stage3_max_live_parameters": 100000000.0, "stage3_max_reuse_distance": 100000000.0, "stage3_gather_16bit_weights_on_model_save": true}, "gradient_clipping": 1.0, "bf16": {"enabled": true}, "activation_checkpointing": {"partition_activations": true, "cpu_checkpointing": true, "contiguous_memory_optimization": false, "number_checkpoints": null, "synchronize_checkpoint_boundary": false, "profile": false}, "wall_clock_breakdown": false, "steps_per_print": 50, "dump_state": false}