# -*- coding: utf-8 -*-
"""
Qwen tokenizer wrapper for openprompt compatibility
"""

import torch
import numpy as np
from openprompt.plms.mlm import MLMTokenizerWrapper
from openprompt.plms.utils import TokenizerWrapper
from transformers.tokenization_utils import PreTrainedTokenizer
from typing import List, Dict, Any, Optional


class QwenTokenizerWrapper(MLMTokenizerWrapper):
    """
    Qwen tokenizer wrapper that handles compatibility issues with openprompt
    """
    
    def __init__(self, 
                 max_seq_length: int, 
                 tokenizer: PreTrainedTokenizer,
                 truncate_method: str = "tail",
                 mask_token_func=None,
                 **kwargs):
        super().__init__(max_seq_length, tokenizer, truncate_method, mask_token_func, **kwargs)
        
        # 确保tokenizer有必要的属性
        if not hasattr(self.tokenizer, 'mask_token') or self.tokenizer.mask_token is None:
            # 对于Qwen，我们可以使用一个特殊的token作为mask
            self.tokenizer.mask_token = '<|mask|>'
            self.tokenizer.mask_token_id = self.tokenizer.vocab_size - 1
        
        # 确保有pad_token
        if not hasattr(self.tokenizer, 'pad_token') or self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
            self.tokenizer.pad_token_id = self.tokenizer.eos_token_id
    
    def get_special_tokens_mask(self, token_ids: List[int], already_has_special_tokens: bool = False) -> List[int]:
        """
        Override to handle Qwen tokenizer compatibility
        """
        try:
            return self.tokenizer.get_special_tokens_mask(token_ids, already_has_special_tokens=already_has_special_tokens)
        except (AssertionError, AttributeError):
            # Fallback: manually create special tokens mask
            special_tokens = set()
            if hasattr(self.tokenizer, 'all_special_ids'):
                special_tokens.update(self.tokenizer.all_special_ids)
            else:
                # 手动添加已知的特殊token
                for attr in ['bos_token_id', 'eos_token_id', 'pad_token_id', 'unk_token_id', 'mask_token_id']:
                    if hasattr(self.tokenizer, attr):
                        token_id = getattr(self.tokenizer, attr)
                        if token_id is not None:
                            special_tokens.add(token_id)
            
            return [1 if token_id in special_tokens else 0 for token_id in token_ids]
    
    def add_special_tokens(self, encoder_inputs: Dict[str, Any]) -> Dict[str, Any]:
        """
        Override to handle special tokens addition for Qwen
        """
        try:
            return super().add_special_tokens(encoder_inputs)
        except (AssertionError, AttributeError) as e:
            # 如果原方法失败，使用简化版本
            print(f"Warning: Using fallback special tokens handling: {e}")
            
            # 简单地返回原始输入，不添加特殊token mask
            for key in encoder_inputs:
                if isinstance(encoder_inputs[key], list):
                    encoder_inputs[key] = torch.tensor(encoder_inputs[key])
            
            return encoder_inputs
    
    def tokenize_one_example(self, wrapped_example, teacher_forcing=None):
        """
        Override tokenize_one_example to handle Qwen-specific issues
        """
        try:
            return super().tokenize_one_example(wrapped_example, teacher_forcing)
        except Exception as e:
            print(f"Warning: Tokenization error, using fallback method: {e}")

            # 简化的tokenization fallback
            # wrapped_example是一个tuple，第一个元素是InputExample对象
            if hasattr(wrapped_example[0], 'text'):
                text = wrapped_example[0].text
            elif hasattr(wrapped_example[0], 'text_a'):
                text = wrapped_example[0].text_a
            else:
                # 如果都没有，尝试直接使用
                text = str(wrapped_example[0])

            # 基本的tokenization
            encoded = self.tokenizer(
                text,
                max_length=self.max_seq_length,
                padding='max_length',
                truncation=True,
                return_tensors='pt'
            )

            return {
                'input_ids': encoded['input_ids'].squeeze(0),
                'attention_mask': encoded['attention_mask'].squeeze(0),
            }


def create_qwen_wrapper(max_seq_length: int, tokenizer: PreTrainedTokenizer, **kwargs):
    """
    Factory function to create Qwen tokenizer wrapper
    """
    return QwenTokenizerWrapper(max_seq_length, tokenizer, **kwargs)
